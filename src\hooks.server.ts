import type { Handle } from '@sveltejs/kit';
import * as auth from '$lib/server/auth';
import { getSecurityConfig } from '$lib/server/security/config';

// Session cache with LRU-like behavior
const SESSION_CACHE = new Map<string, {
  expires: number;
  user: any;
  session: any;
}>();

const CACHE_TTL = 15 * 60 * 1000; // 15 minutes
const CACHE_CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes

// Clean expired sessions periodically
const cleanupInterval = setInterval(() => {
  const now = Date.now();
  let expiredCount = 0;

  for (const [key, value] of SESSION_CACHE.entries()) {
    if (now > value.expires) {
      SESSION_CACHE.delete(key);
      expiredCount++;
    }
  }

  if (expiredCount > 0) {
    console.log(`Cleaned up ${expiredCount} expired sessions from cache`);
  }
}, CACHE_CLEANUP_INTERVAL);

// Ensure cleanup interval is cleared if the server is restarted
if (typeof process !== 'undefined') {
  process.on('SIGTERM', () => clearInterval(cleanupInterval));
}

export const handle: Handle = async ({ event, resolve }) => {
  // Handle Chrome DevTools requests silently
  if (event.url.pathname === '/.well-known/appspecific/com.chrome.devtools.json') {
    return new Response('{}', {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  // Handle Cloudflare proxy headers
  const cfConnectingIp = event.request.headers.get('cf-connecting-ip');
  const xForwardedFor = event.request.headers.get('x-forwarded-for');
  const xRealIp = event.request.headers.get('x-real-ip');

  // Override getClientAddress for Cloudflare tunnels
  if (cfConnectingIp || xForwardedFor || xRealIp) {
    const originalGetClientAddress = event.getClientAddress;
    event.getClientAddress = () => {
      return cfConnectingIp ||
             (xForwardedFor ? xForwardedFor.split(',')[0].trim() : null) ||
             xRealIp ||
             originalGetClientAddress();
    };
  }

  // Handle authentication
  const sessionToken = event.cookies.get(auth.sessionCookieName);

  if (sessionToken) {
    // Try to get session from cache first
    const cachedSession = SESSION_CACHE.get(sessionToken);

    if (cachedSession && cachedSession.expires > Date.now()) {
      // Use cached session data
      event.locals.user = cachedSession.user;
      event.locals.session = cachedSession.session;
    } else {
      // Validate from database
      const { session, user } = await auth.validateSessionToken(sessionToken);

      if (session) {
        // Update cookie and cache valid session
        auth.setSessionTokenCookie(event, sessionToken, session.expiresAt);
        SESSION_CACHE.set(sessionToken, {
          expires: Date.now() + CACHE_TTL,
          user,
          session
        });
      } else {
        // Clear invalid session
        auth.deleteSessionTokenCookie(event);
        SESSION_CACHE.delete(sessionToken);
      }

      // Set locals regardless of validation result
      event.locals.user = user;
      event.locals.session = session;
    }
  } else {
    // No session token
    event.locals.user = null;
    event.locals.session = null;
  }

  // Then handle response
  const locale = event.cookies.get('locale') || 'en';
  const response = await resolve(event, {
    transformPageChunk: ({ html }) => html.replace('<html', `<html lang="${locale}"`)
  });

  // Set security headers
  const securityConfig = getSecurityConfig();
  const csp = securityConfig.SECURITY_HEADERS.CONTENT_SECURITY_POLICY;
  const cspString = Object.entries(csp)
    .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
    .join('; ');

  response.headers.set('Content-Security-Policy', cspString);
  response.headers.set('X-Frame-Options', securityConfig.SECURITY_HEADERS.X_FRAME_OPTIONS);
  response.headers.set('X-Content-Type-Options', securityConfig.SECURITY_HEADERS.X_CONTENT_TYPE_OPTIONS);
  response.headers.set('X-XSS-Protection', securityConfig.SECURITY_HEADERS.X_XSS_PROTECTION);
  response.headers.set('Referrer-Policy', securityConfig.SECURITY_HEADERS.REFERRER_POLICY);
  response.headers.set('Permissions-Policy', securityConfig.SECURITY_HEADERS.PERMISSIONS_POLICY);

  // Set cache headers for static assets
  const path = event.url.pathname;
  if ((path.startsWith('/favicon.') || path.startsWith('/_app/')) ||
      (path.includes('.') && !path.endsWith('.html'))) {
    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
  }

  return response;
};
